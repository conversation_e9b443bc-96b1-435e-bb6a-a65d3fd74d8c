<template>
  <div class="ai-settings">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>
            <RobotOutlined style="margin-right: 8px; color: #1890ff;" />
            智能体管理
          </h2>
          <p class="subtitle">管理和使用您可用的LangChain智能体</p>
        </div>
        <div class="action-section">
          <a-button
            type="primary"
            :loading="refreshing"
            @click="refreshAgentsList"
            style="margin-right: 8px;"
          >
            <ReloadOutlined />
            刷新列表
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="filter-card" style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="6">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索智能体名称或描述"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <!-- 智能体类型筛选已移除 -->
        <a-col :span="4">
          <a-select
            v-model:value="selectedAccess"
            placeholder="访问权限"
            allow-clear
            @change="handleAccessFilter"
            style="width: 100%"
          >
            <a-select-option value="">全部权限</a-select-option>
            <a-select-option value="public">公开</a-select-option>
            <a-select-option value="private">私有</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="10">
          <div class="stats-info">
            <a-statistic
              title="可用智能体"
              :value="total"
              style="display: inline-block; margin-right: 32px;"
            />
            <a-statistic
              title="当前页"
              :value="`${currentPage}/${Math.ceil(total / pageSize)}`"
              style="display: inline-block;"
            />
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 智能体列表 -->
    <div class="agents-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="正在加载智能体列表..." />
      </div>

      <!-- 空状态 -->
      <div v-else-if="agentsList.length === 0" class="empty-state">
        <a-empty
          description="暂无可用的智能体"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        >
          <a-button type="primary" @click="refreshAgentsList">
            重新加载
          </a-button>
        </a-empty>
      </div>

      <!-- 智能体卡片列表 -->
      <div v-else class="agents-grid">
        <a-card
          v-for="agent in agentsList"
          :key="agent.智能体id"
          class="agent-card"
          :hoverable="true"
        >
          <template #title>
            <div class="agent-title">
              <a-avatar :size="32" style="margin-right: 8px; background-color: #1890ff;">
                {{ agent.智能体名称?.charAt(0) || 'A' }}
              </a-avatar>
              <span class="agent-name">{{ agent.智能体名称 }}</span>
            </div>
          </template>

          <template #extra>
            <div class="agent-tags">
              <a-tag :color="agent.是否公开 ? 'green' : 'blue'" size="small">
                {{ agent.是否公开 ? '公开' : '定制' }}
              </a-tag>
              <!-- 智能体类型标签已移除 -->
            </div>
          </template>

          <div class="agent-content">
            <p class="agent-description">
              {{ agent.智能体描述 || '暂无描述' }}
            </p>

            <div class="agent-details">
              <div class="detail-item">
                <span class="label">模型：</span>
                <span class="value">{{ agent.模型名称 || '未指定' }}</span>
              </div>
              <div class="detail-item">
                <span class="label">状态：</span>
                <a-tag :color="(agent.状态 === 'running') ? 'success' : 'default'" size="small">
                  {{ getStatusLabel(agent.状态) }}
                </a-tag>
              </div>
              <div class="detail-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(agent.创建时间) }}</span>
              </div>
            </div>

            <div class="agent-actions">
              <a-button
                type="primary"
                size="small"
                @click="handleUseAgent(agent)"
                :disabled="!agent || agent.状态 !== 'running'"
              >
                <RobotOutlined />
                使用智能体
              </a-button>
              <a-button size="small" @click="handleViewDetails(agent)" style="margin-left: 8px;">
                <EyeOutlined />
                查看详情
              </a-button>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <a-pagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 智能体详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="智能体详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedAgent" class="agent-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <a-avatar :size="32" style="margin-right: 8px; background-color: #1890ff;">
              {{ selectedAgent.智能体名称?.charAt(0) || 'A' }}
            </a-avatar>
            {{ selectedAgent.智能体名称 }}
            <div class="detail-tags">
              <a-tag :color="selectedAgent.是否公开 ? 'green' : 'blue'" size="small">
                {{ selectedAgent.是否公开 ? '公开' : '定制' }}
              </a-tag>
              <a-tag color="purple" size="small">
                {{ getTypeLabel(selectedAgent.智能体类型) }}
              </a-tag>
              <a-tag :color="selectedAgent.状态 === 'running' ? 'success' : 'default'" size="small">
                {{ getStatusLabel(selectedAgent.状态) }}
              </a-tag>
            </div>
          </h3>
        </div>

        <!-- 描述信息 -->
        <div class="detail-section">
          <h4>智能体描述</h4>
          <p class="description-text">
            {{ selectedAgent.智能体描述 || '暂无描述信息' }}
          </p>
        </div>

        <!-- 配置信息 -->
        <div class="detail-section">
          <h4>配置信息</h4>
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="智能体id">
              {{ selectedAgent.智能体id }}
            </a-descriptions-item>
            <a-descriptions-item label="智能体类型">
              {{ getTypeLabel(selectedAgent.智能体类型) }}
            </a-descriptions-item>
            <a-descriptions-item label="使用模型">
              {{ selectedAgent.模型名称 || '未指定' }}
            </a-descriptions-item>
            <a-descriptions-item label="运行状态">
              <a-tag :color="selectedAgent.状态 === 'running' ? 'success' : 'default'" size="small">
                {{ getStatusLabel(selectedAgent.状态) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="访问权限">
              <a-tag :color="selectedAgent.是否公开 ? 'green' : 'blue'" size="small">
                {{ selectedAgent.是否公开 ? '公开智能体' : '私有智能体' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(selectedAgent.创建时间) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <a-button type="primary" size="large" @click="handleUseAgentFromDetail">
            <RobotOutlined />
            使用此智能体
          </a-button>
          <a-button size="large" @click="detailModalVisible = false" style="margin-left: 12px;">
            关闭
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { RobotOutlined, ReloadOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import api from '@/services/api'

defineOptions({ name: 'AISettings' })

const router = useRouter()

// 响应式数据
const loading = ref(true)
const refreshing = ref(false)
const agentsList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 搜索和筛选
const searchKeyword = ref('')
const selectedType = ref('')
const selectedAccess = ref('')

// 详情模态框
const detailModalVisible = ref(false)
const selectedAgent = ref(null)

// 页面初始化
onMounted(async () => {
  await loadAgentsList()
})

// 加载智能体列表
const loadAgentsList = async () => {
  try {
    loading.value = true

    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 添加搜索条件
    if (searchKeyword.value.trim()) {
      params.search = searchKeyword.value.trim()
    }

    if (selectedType.value) {
      params.agent_type = selectedType.value
    }

    if (selectedAccess.value) {
      params.access_type = selectedAccess.value
    }

    console.log('🔍 加载智能体列表，参数:', params)

    const response = await api.post('/user/langchain/agents/available', params)
    console.log('📥 智能体列表响应:', response)

    if (response?.status === 100 && response?.data) {
      const data = response.data
      agentsList.value = data.智能体列表 || []
      total.value = data.总数 || 0

      console.log(`✅ 成功加载 ${agentsList.value.length} 个智能体`)
    } else {
      console.error('❌ 智能体列表加载失败:', response)
      message.error(response?.message || '加载智能体列表失败')
      agentsList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('💥 加载智能体列表失败:', error)
    message.error('加载智能体列表失败，请稍后重试')
    agentsList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 刷新列表
const refreshAgentsList = async () => {
  refreshing.value = true
  try {
    await loadAgentsList()
    message.success('智能体列表已刷新')
  } finally {
    refreshing.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadAgentsList()
}

// 类型筛选
const handleTypeFilter = () => {
  currentPage.value = 1
  loadAgentsList()
}

// 访问权限筛选
const handleAccessFilter = () => {
  currentPage.value = 1
  loadAgentsList()
}

// 分页处理
const handlePageChange = (page, size) => {
  currentPage.value = page
  pageSize.value = size
  loadAgentsList()
}

const handlePageSizeChange = (current, size) => {
  currentPage.value = 1
  pageSize.value = size
  loadAgentsList()
}

// 智能体类型标签映射已移除

// 状态标签映射
const getStatusLabel = (status) => {
  if (!status) return '未知'
  const statusMap = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '错误',
    'pending': '待启动'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return '格式错误'
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.warn('日期格式化错误:', error)
    return '格式错误'
  }
}

// 使用智能体
const handleUseAgent = (agent) => {
  if (!agent) {
    message.error('智能体信息不完整')
    return
  }

  console.log('🚀 使用智能体:', agent)

  // 检查智能体状态
  if (agent.状态 !== 'running') {
    message.warning(`智能体 "${agent.智能体名称 || '未知'}" 当前状态为 ${getStatusLabel(agent.状态)}，无法使用`)
    return
  }

  // 检查必要字段
  if (!agent.智能体id) {
    message.error('智能体id缺失，无法启动')
    return
  }

  // 构建对话页面路径，传递智能体信息
  const chatPath = `/chat?agent_id=${agent.智能体id}&agent_name=${encodeURIComponent(agent.智能体名称 || '智能体')}`

  message.success(`正在启动智能体: ${agent.智能体名称 || '智能体'}`)

  // 跳转到对话页面
  router.push(chatPath)
}

// 从详情模态框使用智能体
const handleUseAgentFromDetail = () => {
  if (selectedAgent.value) {
    handleUseAgent(selectedAgent.value)
    detailModalVisible.value = false
  }
}

// 查看详情
const handleViewDetails = (agent) => {
  if (!agent) {
    message.error('智能体信息不完整')
    return
  }

  console.log('👁️ 查看智能体详情:', agent)
  selectedAgent.value = agent
  detailModalVisible.value = true
}

</script>

<style scoped>
.ai-settings {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.subtitle {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.action-section {
  display: flex;
  align-items: center;
}

/* 筛选卡片 */
.filter-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 智能体容器 */
.agents-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: #8c8c8c;
}

.loading-container p {
  margin-top: 16px;
  font-size: 14px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 智能体网格 */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.agent-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.agent-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.agent-title {
  display: flex;
  align-items: center;
}

.agent-name {
  font-weight: 500;
  font-size: 16px;
}

.agent-tags {
  display: flex;
  gap: 4px;
}

.agent-content {
  padding: 0;
}

.agent-description {
  color: #595959;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  min-height: 42px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-details {
  background: #fafafa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 500;
}

.detail-item .value {
  font-size: 13px;
  color: #262626;
}

.agent-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.agent-actions .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.agent-actions .ant-btn[disabled] {
  opacity: 0.6;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 智能体详情模态框 */
.agent-detail-content {
  padding: 8px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.detail-tags {
  display: flex;
  gap: 4px;
}

.detail-section h4 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.description-text {
  color: #595959;
  line-height: 1.6;
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
  margin: 0;
}

.detail-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .agents-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .ai-settings {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }

  .agents-container {
    padding: 16px;
  }

  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .detail-actions {
    flex-direction: column;
    gap: 8px;
  }

  .detail-actions .ant-btn {
    width: 100%;
  }
}
</style>
