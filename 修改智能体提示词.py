#!/usr/bin/env python3
"""
简单修改智能体提示词脚本
直接在这里写提示词，然后运行即可
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例

# ==================== 在这里写你的提示词 ====================

# 智能体ID
智能体ID = 5

# 系统提示词
系统提示词 = """你是一个专业的智能客服助手，负责处理客户咨询和提供优质服务。

**核心职责：**
1. 友好回应客户问候和咨询
2. 提供准确的产品信息和服务支持
3. 根据需要使用可用工具完成任务

**工作原则：**
- 语言友好、专业、简洁
- 基于知识库内容回答相关问题
- 根据对话需要合理使用工具
- 确保信息准确性和时效性

**工具使用指导：**
- 根据用户需求和对话上下文决定是否使用工具
- 严格按照ReAct格式进行工具调用
- 完成所有必要的工具调用后，输出"Final Answer:"开头的最终回复
- 工具参数格式要正确，避免传递复杂对象

**回复要求：**
- 回答要准确、有帮助
- 语言表达要自然流畅
- 根据用户问题的复杂程度调整回复详细程度"""

# 用户提示词
用户提示词 = """请根据我的问题提供专业的帮助和服务。如果需要使用工具，请按照ReAct格式进行调用。"""

# 自定义变量 (JSON格式)
自定义变量 = """{
  "company_name": "灵邀科技",
  "service_type": "智能客服",
  "max_response_length": 500,
  "tone": "友好专业"
}"""

# ==================== 执行更新 ====================

async def main():
    """主函数"""
    print("🚀 开始更新智能体提示词...")
    
    try:
        # 初始化数据库连接
        if not Postgre_异步连接池实例.已初始化:
            await Postgre_异步连接池实例.初始化数据库连接池()
        
        # 更新系统提示词
        更新SQL = """
        UPDATE langchain_智能体配置表
        SET 系统提示词 = $1, 用户提示词 = $2, 自定义变量 = $3, 更新时间 = $4
        WHERE id = $5
        """
        
        await Postgre_异步连接池实例.执行更新(更新SQL, (
            系统提示词, 
            用户提示词, 
            自定义变量, 
            datetime.now(), 
            智能体ID
        ))
        
        print("✅ 智能体提示词更新成功！")
        print(f"📝 智能体ID: {智能体ID}")
        print(f"📝 系统提示词长度: {len(系统提示词)} 字符")
        print(f"📝 用户提示词长度: {len(用户提示词)} 字符")
        
    except Exception as e:
        print(f"❌ 更新失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
